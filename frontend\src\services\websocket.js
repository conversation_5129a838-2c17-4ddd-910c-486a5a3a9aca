// Implement exponential backoff with jitter
const reconnect = () => {
  const baseDelay = 1000; // 1 second
  const maxDelay = 30000; // 30 seconds
  const retryCount = connectionAttempts.current;
  
  // Calculate delay with exponential backoff and jitter
  const exponentialDelay = Math.min(maxDelay, baseDelay * Math.pow(2, retryCount));
  const jitter = Math.random() * 0.3 * exponentialDelay; // 0-30% jitter
  const delay = exponentialDelay + jitter;
  
  console.log(`Reconnecting in ${Math.round(delay/1000)}s (attempt ${retryCount})`);
  
  reconnectTimeout.current = setTimeout(() => {
    connectWebSocket();
  }, delay);
};