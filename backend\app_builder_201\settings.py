"""
Django settings for app_builder_201 project.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'django-insecure-your-secret-key-here')

# JWT settings
JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', SECRET_KEY)
JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_DELTA = 60 * 60 * 24 * 7  # 7 days in seconds

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'channels',
    'graphene_django',
    'my_app',
    # 'api',  # Commented out because the app doesn't exist
    # 'websockets',  # Commented out because the app doesn't exist
]

# Middleware definition moved below

# For development, allow all origins for WebSockets if DEBUG is True
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True
    CORS_ORIGIN_ALLOW_ALL = True  # This is needed for older versions of django-cors-headers
    CORS_ALLOW_CREDENTIALS = True
    CORS_ALLOW_HEADERS = ['*']
    CORS_ALLOW_METHODS = ['*']
    CORS_ALLOW_ALL_HEADERS = True

ROOT_URLCONF = 'app_builder_201.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'app_builder_201.wsgi.application'
ASGI_APPLICATION = 'app_builder_201.asgi.application'

# Database
# Use PostgreSQL in production/docker, fallback to SQLite for local development
if os.environ.get('USE_POSTGRES', 'False').lower() in ('true', '1', 't'):
    try:
        # Try multiple hostnames for the database to handle Docker networking issues
        db_hosts = ['db', 'postgres', 'localhost', '127.0.0.1']
        db_connected = False

        import socket
        for db_host in db_hosts:
            try:
                # Try to connect to the database host
                print(f"Trying to connect to database at {db_host}:5432")
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)  # Increased timeout
                result = sock.connect_ex((db_host, 5432))
                sock.close()

                if result == 0:
                    print(f"Successfully connected to database at {db_host}:5432")
                    DATABASES = {
                        'default': {
                            'ENGINE': 'django.db.backends.postgresql',
                            'NAME': os.environ.get('POSTGRES_DB', 'myapp'),
                            'USER': os.environ.get('POSTGRES_USER', 'myappuser'),
                            'PASSWORD': os.environ.get('POSTGRES_PASSWORD', 'myapppassword'),
                            'HOST': db_host,
                            'PORT': '5432',
                            'CONN_MAX_AGE': 600,  # Keep connections alive for 10 minutes
                            'OPTIONS': {
                                'connect_timeout': 10,
                                'sslmode': 'prefer',
                            },
                            'TEST': {
                                'NAME': 'test_' + os.environ.get('POSTGRES_DB', 'myapp'),
                            },
                        }
                    }
                    db_connected = True
                    break
                else:
                    print(f"Could not connect to database at {db_host}:5432")
            except Exception as e:
                print(f"Error connecting to database at {db_host}:5432: {e}")

        if not db_connected:
            print("WARNING: Could not connect to PostgreSQL, falling back to SQLite")
            DATABASES = {
                'default': {
                    'ENGINE': 'django.db.backends.sqlite3',
                    'NAME': BASE_DIR / 'db.sqlite3',
                }
            }
    except Exception as e:
        print(f"Error during database configuration: {e}")
        print("Falling back to SQLite")
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': BASE_DIR / 'db.sqlite3',
            }
        }
else:
    # Use SQLite for local development
    print("Using SQLite for local development")
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

# Password validation - temporarily disabled for testing
AUTH_PASSWORD_VALIDATORS = []

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'frontend', 'build', 'static')]

# Configure static file finders
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Configure MIME types for static files
MIMETYPES_FILE = os.path.join(BASE_DIR, 'mime.types')
if os.path.exists(MIMETYPES_FILE):
    import mimetypes
    mimetypes.init([MIMETYPES_FILE])

# Ensure proper MIME type handling with a single middleware definition
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Only include once
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'my_app.csrf_middleware.CustomCsrfMiddleware',  # Use custom CSRF middleware
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'my_app.mime_middleware.MimeTypeMiddleware',
]

# WhiteNoise configuration for serving compressed static files
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
WHITENOISE_MIMETYPES = {
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.map': 'application/json',
}

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Channel layers for WebSockets
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Modify channel layers for development
if DEBUG:
    CHANNEL_LAYERS['default']['CONFIG'] = {
        'expiry': 120  # Longer expiry in development
    }

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        # JSON formatter removed due to configuration issues
    },
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        # File handler disabled in Docker to avoid permission issues
        # 'file': {
        #     'level': 'INFO',
        #     'class': 'logging.FileHandler',
        #     'filename': os.path.join(BASE_DIR, 'django.log'),
        #     'formatter': 'verbose',
        # },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'websockets': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'channels': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'my_app': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'asgi': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'my_app.auth.jwt_auth.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'EXCEPTION_HANDLER': 'my_app.error_handling.custom_exception_handler',
}

# CORS settings - only used if DEBUG is False
if not DEBUG:
    CORS_ALLOWED_ORIGINS = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://frontend:3000",
    ]
    CORS_ALLOW_CREDENTIALS = True

# CSRF settings for cross-origin requests
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://frontend:3000",
    "https://localhost:3000",
    "https://127.0.0.1:3000",
    "https://frontend:3000",
]

# CSRF cookie settings - Development configuration
CSRF_COOKIE_SECURE = False  # Set to True in production with HTTPS
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript access to CSRF token
CSRF_COOKIE_SAMESITE = 'Lax'  # Allow cross-site requests
CSRF_COOKIE_NAME = 'csrftoken'  # Default name for CSRF cookie
CSRF_HEADER_NAME = 'HTTP_X_CSRFTOKEN'  # Header name for CSRF token
CSRF_USE_SESSIONS = False  # Use cookies instead of sessions for CSRF tokens

# WebSocket settings
WEBSOCKET_URL = '/ws/'
CHANNELS_WS_PROTOCOLS = ["graphql-ws"]

# WebSocket allowed origins
if DEBUG:
    # In debug mode, allow all origins
    WEBSOCKET_ALLOWED_ORIGINS = ["*"]
else:
    # In production, only allow specific origins
    WEBSOCKET_ALLOWED_ORIGINS = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://frontend:3000",
        "null",  # Allow connections with no origin (like from file:// or direct IP)
    ]

# Allow all hosts for development
ALLOWED_HOSTS = ['*']


# Redis caching for performance (fallback to local memory)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'app-builder-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# Cache middleware
MIDDLEWARE.insert(1, 'django.middleware.cache.UpdateCacheMiddleware')
MIDDLEWARE.append('django.middleware.cache.FetchFromCacheMiddleware')

CACHE_MIDDLEWARE_ALIAS = 'default'
CACHE_MIDDLEWARE_SECONDS = 300
CACHE_MIDDLEWARE_KEY_PREFIX = 'app_builder'

# Optimized session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = False

# Security Headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'


# Cookie Security - Production settings (override in production)
if not DEBUG:
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Strict'
    CSRF_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    CSRF_COOKIE_SAMESITE = 'Strict'
else:
    # Development settings for easier testing
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
